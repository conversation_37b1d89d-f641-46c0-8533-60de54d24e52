#!/bin/bash

# ====================================================================
# Vidur Model Profiling Script (Simplified)
# 使用vidur内部默认参数组合，只需要配置必要参数
# ====================================================================

# ====================
# 必要配置参数
# ====================
# 模型名称（必须是vidur预定义的模型名）
MODEL_NAME="meta-llama/Llama-3.1-8b"

# 自定义保存目录名称（用于保存结果的目录名）
CUSTOM_MODEL_NAME="meta-llama/Llama-3.1-8b"

# 工作目录
WORK_DIR="/mnt/yrfs/users/yukaifeng/vidur"

# 输出目录（相对于工作目录）
OUTPUT_BASE_DIR="data/profiling/compute/h100"

# ====================
# 硬件配置参数  
# ====================
# GPU数量
NUM_GPUS=8

# 张量并行配置（空格分隔的数字列表）
TENSOR_PARALLEL_WORKERS="1 2 4 8"

# ====================
# 自定义参数配置开关
# ====================
# 是否使用自定义参数（true/false）
# false: 使用vidur默认的内部参数组合遍历
# true: 使用下面自定义的参数值
USE_CUSTOM_PARAMS=true

# ====================
# 自定义模型配置参数（仅当USE_CUSTOM_PARAMS=true时生效）
# ====================
# 最大模型长度（影响内存分配和KV cache大小计算）
MAX_MODEL_LEN=4096

# 最大序列长度（控制profiling的序列长度上限）
# vidur会自动生成0-MAX_SEQ_LEN范围内的多种序列长度组合
MAX_SEQ_LEN=128

# 最大token数（用于MLP profiling，控制token数量的上限）
# vidur会自动生成1-MAX_TOKENS范围内的多种token数量组合
MAX_TOKENS=128

# 块大小（用于分页attention，影响内存分块策略）
BLOCK_SIZE=16

# 批次大小范围
MIN_BATCH_SIZE=1
MAX_BATCH_SIZE=128

# ====================
# 可选配置参数
# ====================
# 是否禁用Ray（true/false）
DISABLE_RAY=false

# 是否只profile decode（true/false，默认false）
PROFILE_ONLY_DECODE=false

# 是否只profile prefill（true/false，默认false）
PROFILE_ONLY_PREFILL=false

# Attention后端（NO_OP/FLASHINFER，推荐NO_OP避免依赖问题）
ATTENTION_BACKEND="NO_OP"

# MLP profiling方法（默认record_function）
PROFILE_METHOD="record_function"

# ====================
# 脚本执行部分
# ====================

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查必要参数
check_params() {
    if [[ -z "$MODEL_NAME" ]]; then
        log_error "MODEL_NAME 未设置"
        exit 1
    fi
    
    if [[ -z "$CUSTOM_MODEL_NAME" ]]; then
        log_error "CUSTOM_MODEL_NAME 未设置"
        exit 1
    fi
    
    log_info "使用预定义模型配置: $MODEL_NAME"
}

# 切换到工作目录
change_to_work_dir() {
    log_info "切换到工作目录: $WORK_DIR"
    cd "$WORK_DIR" || {
        log_error "无法切换到工作目录: $WORK_DIR"
        exit 1
    }
}

# 构建基础参数
build_base_args() {
    BASE_ARGS=""
    
    # 必要参数
    BASE_ARGS="$BASE_ARGS --models \"$MODEL_NAME\""
    BASE_ARGS="$BASE_ARGS --output_dir \"$OUTPUT_BASE_DIR\""
    BASE_ARGS="$BASE_ARGS --num_gpus $NUM_GPUS"
    BASE_ARGS="$BASE_ARGS --num_tensor_parallel_workers $TENSOR_PARALLEL_WORKERS"
    
    # 可选参数
    if [[ "$DISABLE_RAY" == "true" ]]; then
        BASE_ARGS="$BASE_ARGS --disable_ray"
    fi
}

# 构建attention profiling参数
build_attention_args() {
    ATTENTION_ARGS="$BASE_ARGS"
    ATTENTION_ARGS="$ATTENTION_ARGS --attention_backend $ATTENTION_BACKEND"
    
    # 如果使用自定义参数，则添加自定义配置
    if [[ "$USE_CUSTOM_PARAMS" == "true" ]]; then
        ATTENTION_ARGS="$ATTENTION_ARGS --max_model_len $MAX_MODEL_LEN"
        ATTENTION_ARGS="$ATTENTION_ARGS --max_seq_len $MAX_SEQ_LEN"
        ATTENTION_ARGS="$ATTENTION_ARGS --block_size $BLOCK_SIZE"
        ATTENTION_ARGS="$ATTENTION_ARGS --min_batch_size $MIN_BATCH_SIZE"
        ATTENTION_ARGS="$ATTENTION_ARGS --max_batch_size $MAX_BATCH_SIZE"
    fi
    
    if [[ "$PROFILE_ONLY_DECODE" == "true" ]]; then
        ATTENTION_ARGS="$ATTENTION_ARGS --profile_only_decode"
    fi
    
    if [[ "$PROFILE_ONLY_PREFILL" == "true" ]]; then
        ATTENTION_ARGS="$ATTENTION_ARGS --profile_only_prefill"
    fi
}

# 构建MLP profiling参数
build_mlp_args() {
    MLP_ARGS="$BASE_ARGS"
    MLP_ARGS="$MLP_ARGS --profile_method $PROFILE_METHOD"
    
    # 如果使用自定义参数，则添加自定义配置
    if [[ "$USE_CUSTOM_PARAMS" == "true" ]]; then
        MLP_ARGS="$MLP_ARGS --max_tokens $MAX_TOKENS"
    fi
}

# 运行attention profiling
run_attention_profiling() {
    if [[ "$USE_CUSTOM_PARAMS" == "true" ]]; then
        log_info "开始运行 Attention Profiling（使用自定义参数）..."
    else
        log_info "开始运行 Attention Profiling（使用vidur默认参数组合）..."
    fi
    log_info "参数: $ATTENTION_ARGS"
    
    eval "python -m vidur.profiling.attention.main $ATTENTION_ARGS"
    
    if [[ $? -eq 0 ]]; then
        log_success "Attention Profiling 完成"
    else
        log_error "Attention Profiling 失败"
        return 1
    fi
}

# 运行MLP profiling
run_mlp_profiling() {
    if [[ "$USE_CUSTOM_PARAMS" == "true" ]]; then
        log_info "开始运行 MLP Profiling（使用自定义参数）..."
    else
        log_info "开始运行 MLP Profiling（使用vidur默认参数组合）..."
    fi
    log_info "参数: $MLP_ARGS"
    
    eval "python -m vidur.profiling.mlp.main $MLP_ARGS"
    
    if [[ $? -eq 0 ]]; then
        log_success "MLP Profiling 完成"
    else
        log_error "MLP Profiling 失败"
        return 1
    fi
}

# 重命名输出目录
rename_output_dir() {
    # 查找最新的输出目录
    LATEST_ATTENTION_DIR=$(find "$OUTPUT_BASE_DIR" -name "*attention*" -type d | sort | tail -1)
    LATEST_MLP_DIR=$(find "$OUTPUT_BASE_DIR" -name "*mlp*" -type d | sort | tail -1)
    
    if [[ -n "$LATEST_ATTENTION_DIR" ]]; then
        ATTENTION_MODEL_DIR="$LATEST_ATTENTION_DIR/$MODEL_NAME"
        ATTENTION_TARGET_DIR="$OUTPUT_BASE_DIR/$CUSTOM_MODEL_NAME"
        
        if [[ -d "$ATTENTION_MODEL_DIR" ]]; then
            log_info "重命名 attention 结果目录..."
            mkdir -p "$(dirname "$ATTENTION_TARGET_DIR")"
            
            # 如果目标目录已存在，先备份
            if [[ -d "$ATTENTION_TARGET_DIR" ]]; then
                BACKUP_DIR="${ATTENTION_TARGET_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
                log_warn "目标目录已存在，备份到: $BACKUP_DIR"
                mv "$ATTENTION_TARGET_DIR" "$BACKUP_DIR"
            fi
            
            mv "$ATTENTION_MODEL_DIR" "$ATTENTION_TARGET_DIR"
            log_success "Attention 结果已保存到: $ATTENTION_TARGET_DIR"
        fi
    fi
    
    if [[ -n "$LATEST_MLP_DIR" ]]; then
        MLP_MODEL_DIR="$LATEST_MLP_DIR/$MODEL_NAME"
        MLP_TARGET_DIR="$OUTPUT_BASE_DIR/$CUSTOM_MODEL_NAME"
        
        if [[ -d "$MLP_MODEL_DIR" ]]; then
            log_info "重命名 MLP 结果目录..."
            mkdir -p "$(dirname "$MLP_TARGET_DIR")"
            
            # 复制MLP结果到同一目录
            if [[ -d "$MLP_TARGET_DIR" ]]; then
                cp -r "$MLP_MODEL_DIR"/* "$MLP_TARGET_DIR/"
            else
                mv "$MLP_MODEL_DIR" "$MLP_TARGET_DIR"
            fi
            log_success "MLP 结果已保存到: $MLP_TARGET_DIR"
        fi
    fi
}

# 显示配置信息
show_config() {
    log_info "==================== 配置信息 ===================="
    echo "模型名称: $MODEL_NAME"
    echo "自定义名称: $CUSTOM_MODEL_NAME"
    echo "输出目录: $OUTPUT_BASE_DIR"
    echo "GPU数量: $NUM_GPUS"
    echo "张量并行: $TENSOR_PARALLEL_WORKERS"
    echo "Attention后端: $ATTENTION_BACKEND"
    echo "Profile方法: $PROFILE_METHOD"
    echo ""
    
    if [[ "$USE_CUSTOM_PARAMS" == "true" ]]; then
        echo "🔧 使用自定义参数:"
        echo "  最大模型长度: $MAX_MODEL_LEN"
        echo "  最大序列长度: $MAX_SEQ_LEN"
        echo "  最大Token数: $MAX_TOKENS"
        echo "  块大小: $BLOCK_SIZE"
        echo "  批次大小范围: $MIN_BATCH_SIZE - $MAX_BATCH_SIZE"
    else
        echo "⚙️ 使用vidur默认参数组合:"
        echo "  - Max Model Length: 4096 (默认)"
        echo "  - Max Seq Length: 4096 (默认)"
        echo "  - Max Tokens: 4096 (默认)"
        echo "  - Block Size: 16 (默认)"
        echo "  - Batch Size: 1-128 (默认)"
        echo "  - Sequence Length: 0-4096+ (分段采样)"
        echo "  - Token Count: 1-4096+ (分段采样)"
        echo "  - Prefill Chunk: 64-4096+ (分段采样)"
    fi
    
    log_info "=================================================="
}

# 主函数
main() {
    log_info "开始 Vidur 模型 Profiling (简化版)"
    
    # 检查参数
    check_params
    
    # 显示配置
    show_config
    
    # 切换目录
    change_to_work_dir
    
    # 构建参数
    build_base_args
    build_attention_args
    build_mlp_args
    
    # 运行profiling
    log_info "开始 profiling 流程..."
    
    if ! run_attention_profiling; then
        log_error "Attention profiling 失败，退出"
        exit 1
    fi
    
    if ! run_mlp_profiling; then
        log_error "MLP profiling 失败，退出"
        exit 1
    fi
    
    # 重命名输出目录
    rename_output_dir
    
    log_success "所有 profiling 任务完成！"
    log_info "结果保存在: $OUTPUT_BASE_DIR/$CUSTOM_MODEL_NAME"
    log_info "包含了完整的参数组合遍历数据"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi