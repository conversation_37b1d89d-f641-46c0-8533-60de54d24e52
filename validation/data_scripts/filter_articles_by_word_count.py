#!/usr/bin/env python3
"""
过滤 CSV 文件中 Articles 列词语数量在指定范围内的脚本

此脚本读取 bbc-news-summary.csv 文件，过滤保留 Articles 列中词语数量在 2500-3500 范围内的行，
只保留 articles 文本内容，并将结果保存到新的 JSON 文件中。
"""

import pandas as pd
import argparse
import os
import json
from pathlib import Path


def count_words(text):
    """
    计算文本中的词语数量
    
    Args:
        text (str): 输入文本
        
    Returns:
        int: 词语数量
    """
    if pd.isna(text) or text == '':
        return 0
    
    # 简单的词语计数，按空格分割
    words = str(text).split()
    return len(words)


def filter_csv_by_word_count(input_file, output_file=None, min_words=1000, max_words=3500):
    """
    过滤 CSV 文件中 Articles 列词语数量在指定范围内的行，只保留 articles 文本
    
    Args:
        input_file (str): 输入 CSV 文件路径
        output_file (str, optional): 输出 JSON 文件路径。如果为 None，自动生成
        min_words (int): 最小词语数量阈值，默认 2500
        max_words (int): 最大词语数量阈值，默认 3500
        
    Returns:
        str: 输出文件路径
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"输入文件不存在: {input_file}")
    
    print(f"正在读取文件: {input_file}")
    
    # 读取 CSV 文件
    try:
        df = pd.read_csv(input_file)
    except Exception as e:
        raise ValueError(f"读取 CSV 文件失败: {e}")
    
    print(f"原始数据集包含 {len(df)} 行")
    
    # 检查是否存在 Articles 列
    if 'Articles' not in df.columns:
        raise ValueError("CSV 文件中没有找到 'Articles' 列")
    
    # 计算每行 Articles 列的词语数量
    print("正在计算词语数量...")
    df['word_count'] = df['Articles'].apply(count_words)
    
    # 显示词语数量统计信息
    print(f"\n词语数量统计:")
    print(f"最小词语数: {df['word_count'].min()}")
    print(f"最大词语数: {df['word_count'].max()}")
    print(f"平均词语数: {df['word_count'].mean():.2f}")
    print(f"中位数词语数: {df['word_count'].median():.2f}")
    
    # 过滤词语数量在指定范围内的行
    filtered_df = df[(df['word_count'] >= min_words) & (df['word_count'] < max_words)].copy()
    
    print(f"\n过滤条件: {min_words} <= Articles 列词语数量 < {max_words}")
    print(f"过滤后数据集包含 {len(filtered_df)} 行")
    print(f"过滤掉了 {len(df) - len(filtered_df)} 行")
    print(f"保留比例: {len(filtered_df) / len(df) * 100:.2f}%")
    
    # 只保留 Articles 列的文本内容
    articles_list = filtered_df['Articles'].tolist()
    
    # 生成输出文件名
    if output_file is None:
        input_path = Path(input_file)
        output_file = input_path.parent / f"{input_path.stem}_filtered_{min_words}_to_{max_words}_words.json"
    
    # 保存过滤后的数据为 JSON 格式，只包含 articles 文本
    print(f"\n正在保存到: {output_file}")
    try:
        # 只保存 articles 文本列表
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(articles_list, f, indent=2, ensure_ascii=False)
        print("保存成功!")
    except Exception as e:
        raise ValueError(f"保存文件失败: {e}")
    
    return str(output_file)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="过滤 CSV 文件中 Articles 列词语数量在指定范围内的行，只保留 articles 文本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python filter_articles_by_word_count.py ../datasets/bbc-news-summary.csv
  python filter_articles_by_word_count.py ../datasets/bbc-news-summary.csv -o filtered_articles.json
  python filter_articles_by_word_count.py ../datasets/bbc-news-summary.csv --min-words 2000 --max-words 4000
        """
    )
    
    parser.add_argument(
        '-i', '--input',
        dest='input_file',
        help='输入 CSV 文件路径',
        default='/mnt/yrfs/users/yukaifeng/vidur/validation/datasets/bbc-news-summary.csv'
    )
    
    parser.add_argument(
        '-o', '--output',
        dest='output_file',
        help='输出 JSON 文件路径（可选，默认自动生成）',
        default='/mnt/yrfs/users/yukaifeng/vidur/validation/datasets/bbc-news-summary.json'
    )
    
    parser.add_argument(
        '--min-words',
        dest='min_words',
        type=int,
        default=1000,
        help='最小词语数量阈值（默认: 1000）'
    )
    
    parser.add_argument(
        '--max-words',
        dest='max_words',
        type=int,
        default=3500,
        help='最大词语数量阈值（默认: 3500）'
    )
    
    args = parser.parse_args()
    
    try:
        output_file = filter_csv_by_word_count(
            args.input_file,
            args.output_file,
            args.min_words,
            args.max_words
        )
        
        print(f"\n✅ 处理完成!")
        print(f"输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
